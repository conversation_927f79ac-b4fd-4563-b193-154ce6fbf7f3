"""Monitoring and alerting system for Niche Text ETL."""

import os
import re
import smtplib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from pathlib import Path
from loguru import logger

from .config import Config


class LogAnalyzer:
    """Analyzes log files for errors and warnings."""
    
    def __init__(self, log_dir: str, log_file_pattern: str = "*.log"):
        """Initialize log analyzer.
        
        Args:
            log_dir: Directory containing log files
            log_file_pattern: Pattern to match log files
        """
        self.log_dir = Path(log_dir)
        self.log_file_pattern = log_file_pattern
    
    def analyze_logs(self, hours_back: int = 24) -> Dict[str, Any]:
        """Analyze logs for the specified time period.
        
        Args:
            hours_back: Number of hours to look back
            
        Returns:
            Dictionary with analysis results
        """
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        analysis = {
            'period_start': cutoff_time,
            'period_end': datetime.now(),
            'errors': [],
            'warnings': [],
            'total_errors': 0,
            'total_warnings': 0,
            'files_analyzed': [],
            'summary': {}
        }
        
        # Find log files
        log_files = list(self.log_dir.glob(self.log_file_pattern))
        
        for log_file in log_files:
            try:
                file_analysis = self._analyze_file(log_file, cutoff_time)
                analysis['files_analyzed'].append(str(log_file))
                analysis['errors'].extend(file_analysis['errors'])
                analysis['warnings'].extend(file_analysis['warnings'])
                analysis['total_errors'] += file_analysis['error_count']
                analysis['total_warnings'] += file_analysis['warning_count']
            except Exception as e:
                logger.error(f"Failed to analyze log file {log_file}: {e}")
        
        # Generate summary
        analysis['summary'] = self._generate_summary(analysis)
        
        return analysis
    
    def _analyze_file(self, log_file: Path, cutoff_time: datetime) -> Dict[str, Any]:
        """Analyze a single log file.
        
        Args:
            log_file: Path to log file
            cutoff_time: Only analyze entries after this time
            
        Returns:
            Dictionary with file analysis results
        """
        file_analysis = {
            'errors': [],
            'warnings': [],
            'error_count': 0,
            'warning_count': 0
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        entry = self._parse_log_entry(line)
                        if entry and entry['timestamp'] >= cutoff_time:
                            if entry['level'] == 'ERROR':
                                file_analysis['errors'].append({
                                    'file': str(log_file),
                                    'line': line_num,
                                    'timestamp': entry['timestamp'],
                                    'message': entry['message'],
                                    'context': entry.get('context', '')
                                })
                                file_analysis['error_count'] += 1
                            elif entry['level'] == 'WARNING':
                                file_analysis['warnings'].append({
                                    'file': str(log_file),
                                    'line': line_num,
                                    'timestamp': entry['timestamp'],
                                    'message': entry['message'],
                                    'context': entry.get('context', '')
                                })
                                file_analysis['warning_count'] += 1
                    except Exception as e:
                        # Skip malformed log entries
                        continue
        except Exception as e:
            logger.error(f"Error reading log file {log_file}: {e}")
        
        return file_analysis
    
    def _parse_log_entry(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a single log entry.
        
        Args:
            line: Log line to parse
            
        Returns:
            Parsed log entry or None if parsing fails
        """
        # Expected format: "2024-01-01 12:00:00 | ERROR | module:function:line | message"
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| (\w+) \| ([^|]+) \| (.+)'
        match = re.match(pattern, line.strip())
        
        if match:
            timestamp_str, level, context, message = match.groups()
            try:
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                return {
                    'timestamp': timestamp,
                    'level': level,
                    'context': context.strip(),
                    'message': message.strip()
                }
            except ValueError:
                return None
        
        return None
    
    def _generate_summary(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary statistics from analysis.
        
        Args:
            analysis: Analysis results
            
        Returns:
            Summary dictionary
        """
        summary = {
            'has_errors': analysis['total_errors'] > 0,
            'has_warnings': analysis['total_warnings'] > 0,
            'error_categories': {},
            'warning_categories': {},
            'most_common_errors': [],
            'most_common_warnings': []
        }
        
        # Categorize errors and warnings
        error_messages = [error['message'] for error in analysis['errors']]
        warning_messages = [warning['message'] for warning in analysis['warnings']]
        
        # Count message patterns (simplified)
        summary['error_categories'] = self._categorize_messages(error_messages)
        summary['warning_categories'] = self._categorize_messages(warning_messages)
        
        return summary
    
    def _categorize_messages(self, messages: List[str]) -> Dict[str, int]:
        """Categorize messages by common patterns.
        
        Args:
            messages: List of log messages
            
        Returns:
            Dictionary with categories and counts
        """
        categories = {}
        
        for message in messages:
            # Simple categorization based on keywords
            if 'database' in message.lower() or 'connection' in message.lower():
                categories['Database'] = categories.get('Database', 0) + 1
            elif 'parsing' in message.lower() or 'parser' in message.lower():
                categories['Parsing'] = categories.get('Parsing', 0) + 1
            elif 'decompression' in message.lower() or 'gzip' in message.lower():
                categories['Decompression'] = categories.get('Decompression', 0) + 1
            elif 'memory' in message.lower() or 'size' in message.lower():
                categories['Memory/Size'] = categories.get('Memory/Size', 0) + 1
            else:
                categories['Other'] = categories.get('Other', 0) + 1
        
        return categories


class EmailAlerter:
    """Sends email alerts based on log analysis."""
    
    def __init__(self, config: Config, email_enabled: bool = True):
        """Initialize email alerter.

        Args:
            config: Application configuration
            email_enabled: Whether email functionality is enabled
        """
        self.config = config
        self.email_enabled = email_enabled
    
    def send_alert(self, analysis: Dict[str, Any]) -> bool:
        """Send email alert based on log analysis.

        Args:
            analysis: Log analysis results

        Returns:
            True if email sent successfully
        """
        try:
            # Check if email is enabled
            if not self.email_enabled:
                logger.info("Email alerts disabled - skipping alert")
                return True

            # Check if alert should be sent
            if not self._should_send_alert(analysis):
                logger.info("No alert needed - no significant issues found")
                return True

            # Create email content
            subject = self._create_subject(analysis)
            body = self._create_body(analysis)

            # Send email
            return self._send_email(subject, body)

        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")
            return False
    
    def _should_send_alert(self, analysis: Dict[str, Any]) -> bool:
        """Determine if an alert should be sent.
        
        Args:
            analysis: Log analysis results
            
        Returns:
            True if alert should be sent
        """
        error_threshold = self.config.email.error_threshold
        return analysis['total_errors'] >= error_threshold
    
    def _create_subject(self, analysis: Dict[str, Any]) -> str:
        """Create email subject.
        
        Args:
            analysis: Log analysis results
            
        Returns:
            Email subject string
        """
        date_str = analysis['period_end'].strftime('%Y-%m-%d')
        return self.config.email.subject_template.format(date=date_str)
    
    def _create_body(self, analysis: Dict[str, Any]) -> str:
        """Create email body.
        
        Args:
            analysis: Log analysis results
            
        Returns:
            Email body string
        """
        body_parts = [
            "Niche Text ETL Daily Report",
            "=" * 30,
            "",
            f"Analysis Period: {analysis['period_start']} to {analysis['period_end']}",
            "",
            "SUMMARY:",
            f"- Total Errors: {analysis['total_errors']}",
            f"- Total Warnings: {analysis['total_warnings']}",
            f"- Files Analyzed: {len(analysis['files_analyzed'])}",
            ""
        ]
        
        # Add error details if present
        if analysis['errors']:
            body_parts.extend([
                "RECENT ERRORS:",
                "-" * 15
            ])
            
            # Show last 10 errors
            recent_errors = analysis['errors'][-10:]
            for error in recent_errors:
                body_parts.append(
                    f"[{error['timestamp']}] {error['message']}"
                )
            
            if len(analysis['errors']) > 10:
                body_parts.append(f"... and {len(analysis['errors']) - 10} more errors")
            
            body_parts.append("")
        
        # Add warning summary if present
        if analysis['warnings']:
            body_parts.extend([
                f"WARNINGS: {analysis['total_warnings']} total",
                ""
            ])
        
        # Add categories
        if analysis['summary']['error_categories']:
            body_parts.extend([
                "ERROR CATEGORIES:",
                "-" * 17
            ])
            for category, count in analysis['summary']['error_categories'].items():
                body_parts.append(f"- {category}: {count}")
            body_parts.append("")
        
        body_parts.extend([
            "Please check the full logs for more details.",
            "",
            "This is an automated message from the Niche Text ETL system."
        ])
        
        return "\n".join(body_parts)
    
    def _send_email(self, subject: str, body: str) -> bool:
        """Send email using SMTP.
        
        Args:
            subject: Email subject
            body: Email body
            
        Returns:
            True if sent successfully
        """
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.config.email.sender
            msg['To'] = ', '.join(self.config.email.recipients)
            msg['Subject'] = subject

            # Attach body
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            with smtplib.SMTP(self.config.email.smtp_server, self.config.email.smtp_port) as server:
                if self.config.email.use_tls:
                    server.starttls()
                
                # Login if credentials provided
                username = os.getenv('EMAIL_USERNAME')
                password = os.getenv('EMAIL_PASSWORD')
                if username and password:
                    server.login(username, password)
                
                # Send message
                server.send_message(msg)
            
            logger.info(f"Alert email sent successfully to {self.config.email.recipients}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False


class LogRotator:
    """Handles log file rotation and cleanup."""
    
    def __init__(self, log_dir: str, retention_days: int):
        """Initialize log rotator.
        
        Args:
            log_dir: Directory containing log files
            retention_days: Number of days to retain logs
        """
        self.log_dir = Path(log_dir)
        self.retention_days = retention_days
    
    def rotate_logs(self) -> Dict[str, Any]:
        """Rotate and clean up old log files.
        
        Returns:
            Dictionary with rotation results
        """
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        
        results = {
            'files_deleted': [],
            'files_kept': [],
            'total_deleted': 0,
            'total_kept': 0,
            'space_freed': 0
        }
        
        try:
            # Find all log files
            log_files = list(self.log_dir.glob('*.log*'))
            
            for log_file in log_files:
                try:
                    # Get file modification time
                    file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                    
                    if file_mtime < cutoff_date:
                        # Delete old file
                        file_size = log_file.stat().st_size
                        log_file.unlink()
                        results['files_deleted'].append(str(log_file))
                        results['total_deleted'] += 1
                        results['space_freed'] += file_size
                        logger.info(f"Deleted old log file: {log_file}")
                    else:
                        # Keep recent file
                        results['files_kept'].append(str(log_file))
                        results['total_kept'] += 1
                        
                except Exception as e:
                    logger.error(f"Error processing log file {log_file}: {e}")
            
            logger.info(
                f"Log rotation complete: {results['total_deleted']} deleted, "
                f"{results['total_kept']} kept, {results['space_freed']} bytes freed"
            )
            
        except Exception as e:
            logger.error(f"Log rotation failed: {e}")
        
        return results


class Monitor:
    """Main monitoring class that coordinates log analysis, alerting, and rotation."""
    
    def __init__(self, config: Config, email_enabled: bool = True):
        """Initialize monitor.

        Args:
            config: Application configuration
            email_enabled: Whether email functionality is enabled
        """
        self.config = config
        self.log_analyzer = LogAnalyzer(config.logging.log_dir)
        self.email_alerter = EmailAlerter(config, email_enabled)
        self.log_rotator = LogRotator(config.logging.log_dir, config.logging.retention_days)
    
    def run_daily_monitoring(self) -> Dict[str, Any]:
        """Run daily monitoring tasks.
        
        Returns:
            Dictionary with monitoring results
        """
        logger.info("Starting daily monitoring tasks")
        
        results = {
            'timestamp': datetime.now(),
            'log_analysis': None,
            'email_sent': False,
            'log_rotation': None
        }
        
        try:
            # Analyze logs
            logger.info("Analyzing logs for the past 24 hours")
            results['log_analysis'] = self.log_analyzer.analyze_logs(hours_back=24)
            
            # Send alert if needed
            logger.info("Checking if alert should be sent")
            results['email_sent'] = self.email_alerter.send_alert(results['log_analysis'])
            
            # Rotate logs
            logger.info("Rotating old log files")
            results['log_rotation'] = self.log_rotator.rotate_logs()
            
            logger.info("Daily monitoring tasks completed successfully")
            
        except Exception as e:
            logger.error(f"Daily monitoring failed: {e}")
            results['error'] = str(e)
        
        return results
