# Niche Text ETL

A comprehensive data migration and normalization pipeline for extracting text content from SQL Server blob data, with support for multiple document formats, automated monitoring, and intelligent text embeddings.

## Overview

This ETL pipeline extracts binary data from a source SQL Server database, decompresses it if needed, parses various document formats to extract plain text, generates semantic embeddings, and loads the normalized results into a destination database.

### Supported Document Types

- **Microsoft Word** (.doc, .docx) - using `python-docx` and `textract`
- **Microsoft Excel** (.xls, .xlsx) - using `openpyxl` and `textract`
- **PDF** documents - using `pdfminer.six` and `PyPDF2`
- **HTML/XML/JSON/YAML** markup - using `BeautifulSoup` and `html2text`
- **Niche-specific markup** (.nrt, .nxdx) - custom parsers
- **Plain text** (.txt) - direct text extraction

## Features

- **Simplified Execution**: Single command execution with configuration-driven design
- **Text Embeddings**: High-quality semantic embeddings using all-MiniLM-L6-v2
- **Incremental Processing**: Resumes from last processed record
- **Multi-threaded**: Parallel processing for improved performance
- **Robust Error Handling**: Continues processing despite individual record failures
- **Comprehensive Logging**: Detailed logs with configurable retention
- **Email Alerts**: Daily monitoring reports with error summaries
- **Docker Support**: Containerized deployment option
- **Modular Design**: Easy to extend with new parsers

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd niche_text_etl

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy and edit configuration
cp config.yaml.example config.yaml
cp .env.example .env

# Edit config.yaml with your database settings
# Edit .env with your credentials
```

### 3. Run ETL Pipeline

```bash
python main.py
```

That's it! The system will automatically:
- Validate configuration
- Set up database (if enabled)
- Run the ETL pipeline
- Generate text embeddings
- Run monitoring tasks
- Clean up logs (if enabled)

> **Note**: For first-time setup, set `auto_setup_database: true` in config.yaml to automatically create database tables.

## Configuration

The system uses a YAML configuration file (`config.yaml`) with the following sections:

### Database Configuration
```yaml
database:
  source:
    server: "source-server.domain.com"
    database: "source_database"
    # ... other settings
  destination:
    server: "dest-server.domain.com"
    database: "dest_database"
    # ... other settings
```

### Processing Configuration
```yaml
processing:
  batch_size: 1000
  max_workers: 4
  max_records_to_process: null  # Set to a number for testing/debug
  categories_to_process:
    - "ms_word"
    - "ms_excel"
    - "pdf"
    - "markup"
    - "niche_markup"
    - "text"
```

### Text Embeddings Configuration
```yaml
embedding:
  enabled: true                    # Enable semantic embeddings
  model:
    provider: "sentence_transformers"
    model_name: "all-MiniLM-L6-v2" # High-quality, efficient model
    max_tokens: 512
  storage:
    embedding_dimension: 384       # 384-dimensional embeddings
```

### Simplified Execution Configuration
```yaml
execution:
  auto_setup_database: false      # Set to true for first-time setup
  auto_validate_config: true      # Automatically validate before running
  auto_cleanup_logs: false        # Automatically clean up old logs
  exit_on_validation_errors: true # Exit if configuration validation fails
```

### Email Alerts
```yaml
email:
  smtp_server: "smtp.company.com"
  sender: "<EMAIL>"
  recipients:
    - "<EMAIL>"
  alert_schedule: "08:00"
```

## Usage

### Simplified Execution Model

The system now uses a simplified execution model with a single entry point:

```bash
# Run the complete ETL pipeline
python main.py
```

This single command automatically handles:
- Configuration validation
- Database setup (if enabled)
- ETL pipeline execution
- Text embedding generation
- Monitoring tasks
- Log cleanup (if enabled)

### Configuration-Driven Operation

All functionality is controlled through `config.yaml`:

```yaml
# Enable/disable automatic features
execution:
  auto_setup_database: false      # First-time database setup
  auto_validate_config: true      # Pre-execution validation
  auto_cleanup_logs: false        # Automatic log management

monitoring:
  enabled: true                    # Enable monitoring
  run_daily_monitoring: true      # Run after pipeline execution

embedding:
  enabled: true                    # Enable text embeddings
```

### Legacy CLI Support

For backward compatibility, the old CLI commands are documented in `SIMPLIFIED_EXECUTION_GUIDE.md`.

### Docker Deployment

```bash
# Build image
docker build -t niche-text-etl .

# Run with docker-compose
docker-compose up -d

# View logs
docker-compose logs -f
```

## Testing and Debugging

### Debug Mode - Limited Processing

For testing and debugging, you can limit the number of records processed:

#### Method 1: Command Line Option
```bash
# Process only 100 records
python main.py run --limit 100

# Process only 50 records
python main.py run --limit 50
```

#### Method 2: Configuration File
Edit `config.yaml`:
```yaml
processing:
  max_records_to_process: 200  # Process only 200 records
```

#### Method 3: Both (CLI overrides config)
```bash
# This will process 100 records, ignoring config file setting
python main.py run --limit 100
```

### Debug Benefits

- **Fast Testing**: Quickly test changes without processing millions of records
- **Safe Development**: Avoid impacting production data during development
- **Performance Testing**: Measure processing speed on small datasets
- **Error Analysis**: Focus on specific problematic records

### Example Debug Workflow

```bash
# 1. Test with just 10 records
python main.py run --limit 10

# 2. Check logs for any issues
tail -f logs/niche_etl.log

# 3. If successful, try larger batch
python main.py run --limit 100

# 4. Finally run full processing
python main.py run
```

## Architecture

### Core Components

1. **DbClient** - Database connection and batch processing
2. **Decompressor** - Handles gzip decompression
3. **Parser Framework** - Modular content parsers
4. **Pipeline** - Main orchestration with threading
5. **Monitor** - Log analysis and alerting

### Data Flow

```
Source DB → Fetch Batch → Decompress → Parse → Normalize → Destination DB
                ↓
            Update Checkpoint
```

### Threading Model

- Main thread handles database I/O and coordination
- Worker threads process individual records in parallel
- Thread-safe statistics tracking
- Configurable worker pool size

## Monitoring

### Daily Monitoring Tasks

- Analyze logs for errors and warnings
- Send email alerts if error threshold exceeded
- Rotate and clean up old log files
- Generate summary statistics

### Log Analysis

The system automatically categorizes errors by type:
- Database connection issues
- Parsing failures
- Decompression errors
- Memory/size limitations

### Email Alerts

Configurable daily email reports include:
- Error and warning counts
- Recent error messages
- Error categorization
- Processing statistics

## Extending the System

### Adding New Parsers

1. Create a new parser class inheriting from `BaseParser`
2. Implement the `parse()` method and `supported_types` property
3. Register the parser in `ParserFactory`

Example:
```python
class CustomParser(BaseParser):
    @property
    def supported_types(self) -> list:
        return ['custom_format']
    
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        # Implementation here
        pass
```

### Custom Content Categories

Update the SQL query in `database.py` to add new content type mappings:

```sql
CASE
  WHEN real_type LIKE 'new_type%' THEN 'new_category'
  -- ... existing mappings
END AS category
```

## Performance Tuning

### Batch Size
- Larger batches: Better database efficiency, higher memory usage
- Smaller batches: Lower memory usage, more database round trips
- Recommended: 500-2000 records per batch

### Worker Threads
- More workers: Higher CPU utilization, potential resource contention
- Fewer workers: Lower resource usage, slower processing
- Recommended: 2-8 workers depending on server capacity

### Memory Management
- Configure `memory_limit_mb` to prevent large file processing
- Monitor decompressed file sizes
- Consider file size limits in parsers

## Troubleshooting

### Common Issues

1. **Database Connection Failures**
   - Check connection strings and credentials
   - Verify network connectivity
   - Ensure SQL Server drivers are installed

2. **Parsing Errors**
   - Check if required libraries are installed
   - Verify file format detection logic
   - Review parser-specific error messages

3. **Memory Issues**
   - Reduce batch size
   - Lower memory limits
   - Check for memory leaks in parsers

4. **Performance Issues**
   - Adjust worker thread count
   - Optimize database queries
   - Consider indexing on source tables

### Log Analysis

```bash
# Check recent errors
python main.py analyze-logs --hours 24

# View detailed logs
tail -f logs/niche_etl.log

# Search for specific errors
grep "ERROR" logs/niche_etl.log | tail -20
```

## Security Considerations

- Store database credentials in environment variables
- Use SQL Server integrated authentication when possible
- Implement proper access controls on destination database
- Secure SMTP credentials for email alerts
- Regular security updates for dependencies

## License

[Your License Here]

## Support

For issues and questions:
- Check the troubleshooting section
- Review log files for detailed error information
- Contact the development team
