# Simplified Execution Guide

## Overview

The Niche Text ETL has been refactored to use a simplified execution model that eliminates CLI complexity and provides a "run and forget" experience. Users only need to configure the YAML file once and execute `python main.py`.

## Quick Start

1. **Configure the system** by editing `config.yaml`
2. **Install dependencies** (if not already installed):
   ```bash
   pip install -r requirements.txt
   ```
3. **Run the ETL pipeline**:
   ```bash
   python main.py
   ```

That's it! No command-line arguments, no complex CLI options.

## Key Changes

### 1. Single Entry Point
- **Before**: Multiple CLI commands (`run`, `validate`, `setup_db`, `monitor`, etc.)
- **After**: Single command `python main.py` handles everything

### 2. Configuration-Driven Design
All functionality is now controlled through `config.yaml`:

```yaml
# Simplified Execution Configuration
execution:
  auto_setup_database: false      # Automatically run database setup if needed
  auto_validate_config: true      # Automatically validate configuration before running
  auto_cleanup_logs: false        # Automatically clean up old log files
  exit_on_validation_errors: true # Exit if configuration validation fails
  exit_on_pipeline_errors: true   # Exit if pipeline execution fails

# Monitoring
monitoring:
  enabled: true                   # Enable automatic monitoring
  run_daily_monitoring: true     # Run daily monitoring tasks after pipeline execution

# Text Embedding (now using all-MiniLM-L6-v2)
embedding:
  enabled: true                   # Enabled by default with local model
  model:
    provider: "sentence_transformers"
    model_name: "all-MiniLM-L6-v2"
    max_tokens: 512
  storage:
    embedding_dimension: 384      # Correct dimension for all-MiniLM-L6-v2
```

### 3. Enhanced Text Embeddings
- **Model**: Now uses `all-MiniLM-L6-v2` for high-quality, efficient embeddings
- **Local Processing**: No external API dependencies
- **Optimized**: 384-dimensional embeddings with batch processing

### 4. Automatic Error Handling
The system now provides clear, actionable error messages:

```
ERROR: Configuration Error: Configuration file not found: config.yaml
Please ensure the config.yaml file exists in the current directory.
You can copy config.yaml.example to config.yaml and modify it for your environment.
```

## Execution Flow

When you run `python main.py`, the system automatically:

1. **Loads and validates configuration** from `config.yaml`
2. **Sets up logging** based on configuration
3. **Initializes the pipeline** with all components
4. **Validates configuration** (if enabled) and reports any issues
5. **Sets up database** (if auto-setup is enabled)
6. **Cleans up old logs** (if auto-cleanup is enabled)
7. **Runs the main ETL pipeline**
8. **Logs final statistics** and results
9. **Runs monitoring tasks** (if enabled)
10. **Exits with appropriate status codes**

## Configuration Options

### Execution Control
```yaml
execution:
  auto_setup_database: false      # Set to true for first-time setup
  auto_validate_config: true      # Recommended to keep enabled
  auto_cleanup_logs: false        # Set to true for automatic log management
  exit_on_validation_errors: true # Set to false to continue despite warnings
  exit_on_pipeline_errors: true   # Set to false for non-critical environments
```

### Embedding Configuration
```yaml
embedding:
  enabled: true
  model:
    provider: "sentence_transformers"
    model_name: "all-MiniLM-L6-v2"  # High-quality, efficient model
    max_tokens: 512                  # Appropriate for this model
  processing:
    chunk_size: 1000
    chunk_overlap: 100
    batch_size: 32                   # Optimized for local processing
  storage:
    embedding_dimension: 384         # Correct for all-MiniLM-L6-v2
```

## Migration from CLI Version

If you were using the old CLI version:

### Old Way
```bash
python main.py run --limit 100
python main.py validate
python main.py setup_db
python main.py monitor
```

### New Way
1. Set `max_records_to_process: 100` in config.yaml for testing
2. Set `auto_validate_config: true` in config.yaml
3. Set `auto_setup_database: true` in config.yaml (one-time)
4. Set `run_daily_monitoring: true` in config.yaml
5. Run: `python main.py`

## Error Handling

The system provides comprehensive error handling:

- **Configuration Errors**: Clear messages about missing or invalid config files
- **Validation Errors**: Detailed reports of configuration issues
- **Pipeline Errors**: Informative error messages with guidance
- **Graceful Exits**: Proper cleanup and status codes

## Dependencies

The simplified execution model requires these additional dependencies:

```
sentence-transformers>=2.2.2
torch>=2.0.0
transformers>=4.21.0
```

Install with:
```bash
pip install -r requirements.txt
```

## Backward Compatibility

The old CLI interface has been removed, but all functionality is preserved:

- **Database setup**: Now automatic (if enabled) or manual via config
- **Validation**: Now automatic before pipeline execution
- **Monitoring**: Now automatic after pipeline execution
- **Log cleanup**: Now automatic (if enabled)

## Troubleshooting

### Common Issues

1. **"Configuration file not found"**
   - Ensure `config.yaml` exists in the current directory
   - Copy from `config.yaml.example` if needed

2. **"sentence-transformers package not installed"**
   - Run: `pip install sentence-transformers`

3. **Database connection errors**
   - Check database configuration in `config.yaml`
   - Ensure database servers are accessible

4. **Permission errors**
   - Check file permissions for log directory
   - Ensure write access to destination database

### Getting Help

1. Check the logs in the `logs/` directory
2. Run with debug logging by setting `level: "DEBUG"` in config.yaml
3. Review the configuration validation messages
4. Check the original documentation for detailed configuration options

## Benefits

- **Simplified Usage**: Single command execution
- **Better Error Handling**: Clear, actionable error messages
- **Enhanced Embeddings**: High-quality local text embeddings
- **Automatic Operations**: Database setup, validation, monitoring
- **Configuration-Driven**: All settings in one place
- **Backward Compatible**: All original functionality preserved
