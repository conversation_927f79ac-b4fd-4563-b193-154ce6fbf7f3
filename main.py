#!/usr/bin/env python3
"""Main entry point for Niche Text ETL - Simplified Execution Model."""

import sys
import json
from pathlib import Path
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import load_config
from src.logging_setup import setup_logging
from src.pipeline import NicheTextPipeline
from src.monitoring import Monitor


class ConfigurationError(Exception):
    """Raised when configuration is invalid or missing."""
    pass


class PipelineError(Exception):
    """Raised when pipeline execution fails."""
    pass


def load_and_validate_config(config_path: str = "config.yaml"):
    """Load and validate configuration with proper error handling.

    Args:
        config_path: Path to configuration file

    Returns:
        Loaded configuration object

    Raises:
        ConfigurationError: If configuration is invalid or missing
    """
    try:
        # Check if config file exists
        if not Path(config_path).exists():
            raise ConfigurationError(
                f"Configuration file not found: {config_path}\n"
                f"Please ensure the config.yaml file exists in the current directory.\n"
                f"You can copy config.yaml.example to config.yaml and modify it for your environment."
            )

        # Load configuration
        config = load_config(config_path)
        logger.info(f"Configuration loaded successfully from: {config_path}")

        return config

    except Exception as e:
        if isinstance(e, ConfigurationError):
            raise
        raise ConfigurationError(f"Failed to load configuration from {config_path}: {e}")


def setup_database_if_needed(pipeline: NicheTextPipeline, config):
    """Set up database if auto-setup is enabled.

    Args:
        pipeline: Pipeline instance
        config: Configuration object
    """
    if config.execution.auto_setup_database:
        try:
            logger.info("Auto-setting up database...")
            sql_script_path = Path(__file__).parent / "sql" / "create_destination_tables.sql"

            if not sql_script_path.exists():
                logger.warning(f"Database setup script not found: {sql_script_path}")
                return

            with open(sql_script_path, 'r') as f:
                sql_template = f.read()

            # Format the SQL script with names from config.yaml
            formatted_sql = sql_template.format(
                destination_server=config.database['destination'].server,
                destination_database=config.database['destination'].database,
                main_processed_documents_table=config.database_objects.main_processed_documents_table,
                etl_checkpoints_table=config.database_objects.etl_checkpoints_table,
                processing_statistics_table=config.database_objects.processing_statistics_table,
                document_chunk_embeddings_table=config.database_objects.document_chunk_embeddings_table,
                processed_documents_view=config.database_objects.processed_documents_view,
                update_statistics_procedure=config.database_objects.update_statistics_procedure,
                initial_checkpoint_process_name=config.database_objects.initial_checkpoint_process_name
            )

            pipeline.dest_db.execute_script(formatted_sql)
            logger.info("Database setup completed successfully")

        except Exception as e:
            logger.error(f"Database setup failed: {e}")
            if config.execution.exit_on_pipeline_errors:
                raise PipelineError(f"Database setup failed: {e}")


def run_monitoring_if_enabled(config, email_enabled: bool = True):
    """Run monitoring tasks if enabled.

    Args:
        config: Configuration object
        email_enabled: Whether email functionality is enabled
    """
    if config.monitoring.enabled and config.monitoring.run_daily_monitoring:
        try:
            logger.info("Running daily monitoring tasks...")
            monitor = Monitor(config, email_enabled)
            results = monitor.run_daily_monitoring()

            logger.info(f"Monitoring completed: {json.dumps(results, indent=2, default=str)}")

            if 'error' in results:
                logger.warning("Monitoring tasks completed with errors")

        except Exception as e:
            logger.error(f"Monitoring failed: {e}")
            # Don't exit on monitoring failures - they're not critical


def cleanup_logs_if_enabled(config, email_enabled: bool = True):
    """Clean up old logs if auto-cleanup is enabled.

    Args:
        config: Configuration object
        email_enabled: Whether email functionality is enabled
    """
    if config.execution.auto_cleanup_logs:
        try:
            logger.info("Auto-cleaning up old log files...")
            monitor = Monitor(config, email_enabled)
            results = monitor.log_rotator.rotate_logs()

            logger.info(f"Log cleanup completed: {results['total_deleted']} files deleted, "
                       f"{results['space_freed']} bytes freed")

        except Exception as e:
            logger.warning(f"Log cleanup failed: {e}")
            # Don't exit on cleanup failures - they're not critical


def main():
    """Main entry point for simplified ETL execution."""
    try:
        print("Niche Text ETL - Starting...")

        # Step 1: Load and validate configuration
        config = load_and_validate_config()

        # Step 2: Setup logging
        setup_logging(config.logging)
        logger.info("Niche Text ETL starting with simplified execution model")

        # Step 3: Initialize pipeline
        logger.info("Initializing pipeline...")
        pipeline = NicheTextPipeline(config)

        # Step 4: Validate configuration if enabled
        email_enabled = True  # Default value
        if config.execution.auto_validate_config:
            logger.info("Validating configuration...")
            validation = pipeline.validate_configuration()

            # Extract module flags
            email_enabled = validation.get('module_flags', {}).get('email_enabled', True)

            if not validation['valid']:
                logger.error("Configuration validation failed:")
                for error in validation['errors']:
                    logger.error(f"  - {error}")

                if config.execution.exit_on_validation_errors:
                    raise ConfigurationError("Configuration validation failed")
                else:
                    logger.warning("Continuing despite validation errors...")

            if validation['warnings']:
                logger.warning("Configuration warnings:")
                for warning in validation['warnings']:
                    logger.warning(f"  - {warning}")

            logger.info("Configuration validation completed")

        # Step 5: Setup database if needed
        setup_database_if_needed(pipeline, config)

        # Step 6: Clean up logs if enabled
        cleanup_logs_if_enabled(config, email_enabled)

        # Step 7: Run the main pipeline
        logger.info("Starting ETL pipeline execution...")
        stats = pipeline.run()

        # Step 8: Log final statistics
        logger.info("Pipeline execution completed successfully")
        logger.info(f"Final statistics: {json.dumps(stats, indent=2, default=str)}")

        # Step 9: Run monitoring if enabled
        run_monitoring_if_enabled(config, email_enabled)

        # Step 10: Check for failures and exit appropriately
        if stats['records_failed'] > 0:
            logger.warning(f"Pipeline completed with {stats['records_failed']} failures")
            if config.execution.exit_on_pipeline_errors:
                sys.exit(1)

        logger.info("Niche Text ETL completed successfully")
        print("Niche Text ETL - Completed successfully!")

    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
        print("Niche Text ETL - Interrupted by user")
        sys.exit(130)

    except ConfigurationError as e:
        error_msg = f"Configuration Error: {e}"
        logger.error(error_msg)
        print(f"ERROR: {error_msg}")
        print("\nPlease check your config.yaml file and ensure all required settings are properly configured.")
        sys.exit(1)

    except PipelineError as e:
        error_msg = f"Pipeline Error: {e}"
        logger.error(error_msg)
        print(f"ERROR: {error_msg}")
        sys.exit(1)

    except Exception as e:
        error_msg = f"Unexpected error: {e}"
        logger.error(error_msg)
        print(f"ERROR: {error_msg}")
        print("\nAn unexpected error occurred. Please check the logs for more details.")
        sys.exit(1)


if __name__ == '__main__':
    main()
